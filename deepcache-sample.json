{"last_node_id": 12, "last_link_id": 11, "nodes": [{"id": 7, "type": "CLIPTextEncode", "pos": [413, 389], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5, "localized_name": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6], "slot_index": 0, "localized_name": "CONDITIONING"}], "properties": {"Node name for S&R": "CLIPTextEncode", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["text, watermark"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [415, 186], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3, "localized_name": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [4], "slot_index": 0, "localized_name": "CONDITIONING"}], "properties": {"Node name for S&R": "CLIPTextEncode", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["beautiful scenery nature glass bottle landscape, , purple galaxy bottle,"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [863, 186], "size": [315, 262], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 11, "localized_name": "model"}, {"name": "positive", "type": "CONDITIONING", "link": 4, "localized_name": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 6, "localized_name": "negative"}, {"name": "latent_image", "type": "LATENT", "link": 2, "localized_name": "latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0, "localized_name": "LATENT"}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [156680208700286, "fixed", 20, 8, "euler", "normal", 1]}, {"id": 8, "type": "VAEDecode", "pos": [1209, 188], "size": [210, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7, "localized_name": "samples"}, {"name": "vae", "type": "VAE", "link": 8, "localized_name": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9], "slot_index": 0, "localized_name": "IMAGE"}], "properties": {"Node name for S&R": "VAEDecode", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1451, 189], "size": [210, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9, "localized_name": "images"}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["ComfyUI"]}, {"id": 10, "type": "DeepCache", "pos": [37, 187], "size": [315, 130], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 10, "localized_name": "model"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [11], "shape": 3, "slot_index": 0, "localized_name": "MODEL"}], "properties": {"Node name for S&R": "DeepCache", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [5, 3, 350, 1000], "color": "#c09430"}, {"id": 11, "type": "Note", "pos": [30, 353], "size": [309.5799865722656, 202.76998901367188], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["Deepcache starts doing its stuff at \"start_step\", if it is 0,  it works on all steps but that lowers the output quality and derives much from the original output. 350 aka one third of the steps as starting point, produces almost the same output and significantly makes the generation faster. "], "color": "#432", "bgcolor": "#653"}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-377, 220], "size": [315, 98], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [10], "slot_index": 0, "localized_name": "MODEL"}, {"name": "CLIP", "type": "CLIP", "links": [3, 5], "slot_index": 1, "localized_name": "CLIP"}, {"name": "VAE", "type": "VAE", "links": [8], "slot_index": 2, "localized_name": "VAE"}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["(pony)autismmixConfetti.safetensors"]}, {"id": 5, "type": "EmptyLatentImage", "pos": [473, 609], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0, "localized_name": "LATENT"}], "properties": {"Node name for S&R": "EmptyLatentImage", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [1024, 1024, 1]}, {"id": 12, "type": "Note", "pos": [-361, 357], "size": [309.5799865722656, 202.76998901367188], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["any sdxl checkpoint you would like here"], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [3, 4, 1, 6, 0, "CLIP"], [4, 6, 0, 3, 1, "CONDITIONING"], [5, 4, 1, 7, 0, "CLIP"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [9, 8, 0, 9, 0, "IMAGE"], [10, 4, 0, 10, 0, "MODEL"], [11, 10, 0, 3, 0, "MODEL"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1, "offset": {"0": 413, "1": -26}}, "node_versions": {"comfy-core": "0.3.13"}, "ue_links": []}, "version": 0.4}