{"last_node_id": 25, "last_link_id": 40, "nodes": [{"id": 13, "type": "SamplerCustomAdvanced", "pos": [842, 215], "size": [355.20001220703125, 106], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 37, "slot_index": 0, "localized_name": "noise"}, {"name": "guider", "type": "GUIDER", "link": 30, "slot_index": 1, "localized_name": "guider"}, {"name": "sampler", "type": "SAMPLER", "link": 19, "slot_index": 2, "localized_name": "sampler"}, {"name": "sigmas", "type": "SIGMAS", "link": 20, "slot_index": 3, "localized_name": "sigmas"}, {"name": "latent_image", "type": "LATENT", "link": 23, "slot_index": 4, "localized_name": "latent_image"}], "outputs": [{"name": "output", "type": "LATENT", "links": [24], "slot_index": 0, "shape": 3, "localized_name": "output"}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3, "localized_name": "denoised_output"}], "properties": {"Node name for S&R": "SamplerCustomAdvanced", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": []}, {"id": 22, "type": "BasicGuider", "pos": [559, 125], "size": [241.79998779296875, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 39, "slot_index": 0, "localized_name": "model"}, {"name": "conditioning", "type": "CONDITIONING", "link": 40, "slot_index": 1, "localized_name": "conditioning"}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [30], "slot_index": 0, "shape": 3, "localized_name": "GUIDER"}], "properties": {"Node name for S&R": "BasicGuider", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": []}, {"id": 16, "type": "KSamplerSelect", "pos": [470, 749], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [19], "shape": 3, "localized_name": "SAMPLER"}], "properties": {"Node name for S&R": "KSamplerSelect", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["euler"]}, {"id": 8, "type": "VAEDecode", "pos": [1248, 192], "size": [210, 46], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 24, "localized_name": "samples"}, {"name": "vae", "type": "VAE", "link": 12, "localized_name": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9], "slot_index": 0, "localized_name": "IMAGE"}], "properties": {"Node name for S&R": "VAEDecode", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": []}, {"id": 25, "type": "RandomNoise", "pos": [470, 611], "size": [315, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [37], "shape": 3, "localized_name": "NOISE"}], "properties": {"Node name for S&R": "RandomNoise", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [845373432002232, "randomize"]}, {"id": 5, "type": "EmptyLatentImage", "pos": [473, 450], "size": [315, 106], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [23], "slot_index": 0, "localized_name": "LATENT"}], "properties": {"Node name for S&R": "EmptyLatentImage", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [1024, 1024, 1]}, {"id": 17, "type": "BasicScheduler", "pos": [468, 867], "size": [315, 106], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 38, "slot_index": 0, "localized_name": "model"}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [20], "shape": 3, "localized_name": "SIGMAS"}], "properties": {"Node name for S&R": "BasicScheduler", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["simple", 20, 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [375, 221], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 10, "localized_name": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [40], "slot_index": 0, "localized_name": "CONDITIONING"}], "properties": {"Node name for S&R": "CLIPTextEncode", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["the story of life"]}, {"id": 9, "type": "SaveImage", "pos": [1232.1500244140625, 91.7800064086914], "size": [985.3012084960938, 1060.3828125], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9, "localized_name": "images"}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["ComfyUI"]}, {"id": 10, "type": "VAELoader", "pos": [864, 384], "size": [315, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [12], "slot_index": 0, "shape": 3, "localized_name": "VAE"}], "properties": {"Node name for S&R": "VAELoader", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["ae.sft"]}, {"id": 11, "type": "DualCLIPLoader", "pos": [28, 239], "size": [317.4200134277344, 138.6699981689453], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [10], "slot_index": 0, "shape": 3, "localized_name": "CLIP"}], "properties": {"Node name for S&R": "DualCLIPLoader", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["t5xxl_fp8_e4m3fn_scaled.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 12, "type": "UNETLoader", "pos": [11.900003433227539, 127], "size": [315, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [38, 39], "slot_index": 0, "shape": 3, "localized_name": "MODEL"}], "properties": {"Node name for S&R": "UNETLoader", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["jibMixFlux_v7PixelHeavenBeta.safetensors", "fp8_e4m3fn_fast"]}], "links": [[9, 8, 0, 9, 0, "IMAGE"], [10, 11, 0, 6, 0, "CLIP"], [12, 10, 0, 8, 1, "VAE"], [19, 16, 0, 13, 2, "SAMPLER"], [20, 17, 0, 13, 3, "SIGMAS"], [23, 5, 0, 13, 4, "LATENT"], [24, 13, 0, 8, 0, "LATENT"], [30, 22, 0, 13, 1, "GUIDER"], [37, 25, 0, 13, 0, "NOISE"], [38, 12, 0, 17, 0, "MODEL"], [39, 12, 0, 22, 0, "MODEL"], [40, 6, 0, 22, 1, "CONDITIONING"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.8264462809917354, "offset": [50.13532714841663, -6.541741943358501]}, "node_versions": {"comfy-core": "v0.3.10-35-g916d1e14"}}, "version": 0.4}