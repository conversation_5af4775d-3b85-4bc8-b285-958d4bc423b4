{"last_node_id": 551, "last_link_id": 1595, "nodes": [{"id": 544, "type": "UNETLoader", "pos": [-2932.108642578125, -304.708984375], "size": [360.0400085449219, 82], "flags": {"collapsed": false}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1595], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["jibMixFlux_v7PixelHeavenBeta.safetensors", "fp8_e4m3fn_fast"]}, {"id": 545, "type": "DualCLIPLoader", "pos": [-2916.203857421875, -176.76856994628906], "size": [340.67999267578125, 130.1999969482422], "flags": {"collapsed": false}, "order": 1, "mode": 0, "showAdvanced": false, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [1587], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors", "t5xxl_fp8_e4m3fn_scaled.safetensors", "flux", "cpu"]}, {"id": 551, "type": "VAELoader", "pos": [-2910.156982421875, 8.161553382873535], "size": [334.6300048828125, 58], "flags": {"collapsed": false}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1594], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["ae.sft"]}, {"id": 547, "type": "SimpleMathInt+", "pos": [-2910.26611328125, 167.7602081298828], "size": [210, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [1590, 1591], "slot_index": 0}], "title": "width", "properties": {"Node name for S&R": "SimpleMathInt+", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [1024]}, {"id": 546, "type": "SimpleMathInt+", "pos": [-2915.1005859375, 297.08966064453125], "size": [210, 68.88999938964844], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [1588, 1589], "slot_index": 0}], "title": "height", "properties": {"Node name for S&R": "SimpleMathInt+", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [1024]}, {"id": 548, "type": "KSamplerSelect", "pos": [-2910.154296875, 424.5264587402344], "size": [210, 58], "flags": {"collapsed": false}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [1592], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["euler"]}, {"id": 549, "type": "RandomNoise", "pos": [-2915.599609375, 540.2174072265625], "size": [246.4936065673828, 82], "flags": {"collapsed": false}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [1593], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RandomNoise", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [266240453722619, "randomize"]}, {"id": 245, "type": "CLIPTextEncode", "pos": [-2545.322021484375, -175.79457092285156], "size": [328.3402099609375, 192.07411193847656], "flags": {"collapsed": false}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1587}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [488], "slot_index": 0, "shape": 3}], "title": "Positive Prompt", "properties": {"Node name for S&R": "CLIPTextEncode", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["tree of life"], "color": "#346434", "shape": 1}, {"id": 60, "type": "FluxGuidance", "pos": [-2186.099609375, -93.33563995361328], "size": [211.60000610351562, 58], "flags": {"collapsed": false}, "order": 10, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 488}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [349], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [3.5], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 13, "type": "SamplerCustomAdvanced", "pos": [-1704.8145751953125, 128.52374267578125], "size": [236.8000030517578, 106], "flags": {"collapsed": false}, "order": 13, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 1593, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 30, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 1592, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 20, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 257, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [856], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 17, "type": "BasicScheduler", "pos": [-1971.3857421875, 317.1419982910156], "size": [210, 106], "flags": {"collapsed": false}, "order": 11, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 391, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [20], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["simple", 20, 1], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 5, "type": "EmptyLatentImage", "pos": [-2612.48779296875, 302.22467041015625], "size": [210, 78], "flags": {"collapsed": false}, "order": 8, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 1591, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1588, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [257], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [832, 1216, 1], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 61, "type": "ModelSamplingFlux", "pos": [-2377.703369140625, 123.34190368652344], "size": [210, 122], "flags": {"collapsed": false}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1595}, {"name": "width", "type": "INT", "link": 1590, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1589, "widget": {"name": "height"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [390, 391], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ModelSamplingFlux", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [1.1500000000000001, 0.1, 1024, 1024], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 22, "type": "BasicGuider", "pos": [-1941.7586669921875, 44.95262908935547], "size": [161.1999969482422, 46], "flags": {"collapsed": false}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 390, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 349, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [30], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 455, "type": "VAEDecode", "pos": [-1425.1693115234375, 144.8216552734375], "size": [140, 46], "flags": {"collapsed": false}, "order": 14, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 856}, {"name": "vae", "type": "VAE", "link": 1594}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1200], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [], "color": "#38291f", "shape": 1}, {"id": 482, "type": "SaveImage", "pos": [-1224.************, 5.1243672370910645], "size": [560.9000244140625, 442.2123718261719], "flags": {"collapsed": false}, "order": 15, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1200}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["flux"], "color": "#38291f", "shape": 1}], "links": [[20, 17, 0, 13, 3, "SIGMAS"], [30, 22, 0, 13, 1, "GUIDER"], [257, 5, 0, 13, 4, "LATENT"], [349, 60, 0, 22, 1, "CONDITIONING"], [390, 61, 0, 22, 0, "MODEL"], [391, 61, 0, 17, 0, "MODEL"], [488, 245, 0, 60, 0, "CONDITIONING"], [856, 13, 0, 455, 0, "LATENT"], [1200, 455, 0, 482, 0, "IMAGE"], [1587, 545, 0, 245, 0, "CLIP"], [1588, 546, 0, 5, 1, "INT"], [1589, 546, 0, 61, 2, "INT"], [1590, 547, 0, 61, 1, "INT"], [1591, 547, 0, 5, 0, "INT"], [1592, 548, 0, 13, 2, "SAMPLER"], [1593, 549, 0, 13, 0, "NOISE"], [1594, 551, 0, 455, 1, "VAE"], [1595, 544, 0, 61, 0, "MODEL"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.6830134553650705, "offset": [3016.************, 496.8424580566417]}, "workspace_info": {"id": "MWno_uJ3vcGtoyt42dDp0"}, "groupNodes": {}, "ue_links": [], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "node_versions": {"comfy-core": "v0.3.10-35-g916d1e14"}}, "version": 0.4}